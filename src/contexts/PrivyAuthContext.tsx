import { useAppHeaderSelector } from "@/components/Headers/EntryHeader/header.store";
import { PrivyAuthContextProps } from "@/types";
import { useLogout, usePrivy, useWallets, useModalStatus } from "@privy-io/react-auth";
import { useMemo } from "react";
import { createContext } from "use-context-selector";

const initialValue: PrivyAuthContextProps = {
  wallets: null,
  ready: false,
  authenticated: false,
  onLogin: () => { },
  onLogout: () => { },
  confirmLogout: () => { },
  onCreateWallet: async () => { },
  getJWT: async () => "",
  isOpen: false,
};

const PrivyAuthContext = createContext<PrivyAuthContextProps>(initialValue);

export function PrivyAuthProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { ready, authenticated, user, createWallet, getAccessToken, login } = usePrivy();
  const { wallets } = useWallets();
  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();
  const { isOpen } = useModalStatus();

  const { logout } = useLogout({
    onSuccess: () => {
      window.location.href = "https://ibi.cash";
    },
  });

  const onLogin = async () => {
    login();
  };

  const onLogout = () => {
    if (authenticated) {
      toggleConfirmLogout();
    }
  };

  const confirmLogout = (direct?: boolean) => {
    if (authenticated) {
      !direct && toggleConfirmLogout();
      logout();
    }
  };

  const onCreateWallet = async () => {
    await createWallet();
  };

  const getJWT = async () => {
    const jwt = await getAccessToken();
    return jwt;
  };

  const value = useMemo(
    () => ({
      user,
      wallets,
      ready,
      authenticated,
      onLogin,
      onLogout,
      confirmLogout,
      onCreateWallet,
      getJWT,
      isOpen,
    }),
    [user, wallets, ready, onLogin, onLogout, confirmLogout, onCreateWallet, getJWT, authenticated, isOpen],
  );

  return <PrivyAuthContext.Provider value={value}>{children}</PrivyAuthContext.Provider>;
}

export default PrivyAuthContext;
