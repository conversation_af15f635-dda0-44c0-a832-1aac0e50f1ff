import { useContextSelector } from "use-context-selector";

import PrivyAuthContext from "../../../contexts/PrivyAuthContext";
import { useJWTManagerSelector } from "./store";

export interface UseJWTManagerReturn {
  getJWT: () => Promise<string>;
  getCachedJWT: () => string | null;
  clearJWTCache: () => void;
  isJWTValid: () => boolean;
  refreshJWT: () => Promise<string>;
  getJWTWithRetry: (maxRetries?: number) => Promise<string>;
  lastRefresh: number;
}

export const useJWTManager = (): UseJWTManagerReturn => {
  const getAccessToken = useContextSelector(PrivyAuthContext, (auth) => auth.getJWT);

  const cachedJWT = useJWTManagerSelector.use.cachedJWT();
  const jwtExpiry = useJWTManagerSelector.use.jwtExpiry();
  const lastRefresh = useJWTManagerSelector.use.lastRefresh();
  const setCachedJWT = useJWTManagerSelector.use.setCachedJWT();
  const setJWTExpiry = useJWTManagerSelector.use.setJWTExpiry();
  const clearJWTCache = useJWTManagerSelector.use.clearJWTCache();
  const updateLastRefresh = useJWTManagerSelector.use.updateLastRefresh();

  const isJWTValid = (): boolean => {
    if (!cachedJWT || !jwtExpiry) return false;
    return Date.now() < jwtExpiry;
  };

  const refreshJWT = async (): Promise<string> => {
    try {
      const jwt = await getAccessToken();

      if (!jwt) {
        throw new Error("Failed to get JWT from Privy");
      }

      // Cache the JWT with expiry (assuming 1 hour expiry)
      const expiry = Date.now() + (60 * 60 * 1000); // 1 hour
      setCachedJWT(jwt);
      setJWTExpiry(expiry);
      updateLastRefresh();

      return jwt;
    } catch (error) {
      clearJWTCache();
      throw error;
    }
  };

  const getJWT = async (): Promise<string> => {
    // Return cached JWT if still valid
    if (isJWTValid() && cachedJWT) {
      return cachedJWT;
    }

    return refreshJWT();
  };

  const getJWTWithRetry = async (maxRetries: number = 3): Promise<string> => {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await getJWT();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("Unknown error");

        if (attempt === maxRetries) {
          break;
        }

        // Wait before retry (exponential backoff)
        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError || new Error("Failed to get JWT after retries");
  };

  const getCachedJWT = (): string | null => {
    return isJWTValid() ? cachedJWT : null;
  };

  return {
    getJWT,
    getCachedJWT,
    clearJWTCache,
    isJWTValid,
    refreshJWT,
    getJWTWithRetry,
    lastRefresh,
  };
};
