import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

interface JWTManagerStore {
  cachedJWT: string | null;
  jwtExpiry: number | null;
  lastRefresh: number;
  refreshCount: number;
  lastError: string | null;

  // Actions
  setCachedJWT: (jwt: string) => void;
  setJWTExpiry: (expiry: number) => void;
  clearJWTCache: () => void;
  updateLastRefresh: () => void;
  incrementRefreshCount: () => void;
  setLastError: (error: string | null) => void;
}

const useJWTManagerStore = create<JWTManagerStore>((set) => ({
  cachedJWT: null,
  jwtExpiry: null,
  lastRefresh: 0,
  refreshCount: 0,
  lastError: null,

  setCachedJWT: (jwt) => set((state) => ({
    cachedJWT: jwt,
    lastError: null,
    refreshCount: state.refreshCount + 1,
  })),
  setJWTExpiry: (expiry) => set({ jwtExpiry: expiry }),
  clearJWTCache: () => set({
    cachedJWT: null,
    jwtExpiry: null,
    lastRefresh: Date.now(),
    lastError: null,
  }),
  updateLastRefresh: () => set({ lastRefresh: Date.now() }),
  incrementRefreshCount: () => set((state) => ({
    refreshCount: state.refreshCount + 1
  })),
  setLastError: (error) => set({ lastError: error }),
}));

export const useJWTManagerSelector = createSelectors(useJWTManagerStore);
