import { ConnectedWallet, User } from "@privy-io/react-auth";
import { useContextSelector } from "use-context-selector";
import { useEffect } from "react";

import PrivyAuthContext from "../../../contexts/PrivyAuthContext";
import { usePrivyStateSelector } from "./store";

export interface UsePrivyStateReturn {
  user: User | null;
  wallets: ConnectedWallet[] | null;
  ready: boolean;
  authenticated: boolean;
  isOpen: boolean;
  onLogin: () => void;
  onLogout: () => void;
  confirmLogout: (direct?: boolean) => void;
  onCreateWallet: () => Promise<void>;
  syncWithContext: () => void;
}

export const usePrivyState = (): UsePrivyStateReturn => {
  // Get state from Zustand store
  const user = usePrivyStateSelector.use.user();
  const wallets = usePrivyStateSelector.use.wallets();
  const ready = usePrivyStateSelector.use.ready();
  const authenticated = usePrivyStateSelector.use.authenticated();
  const isOpen = usePrivyStateSelector.use.isOpen();

  // Get setters from store
  const setUser = usePrivyStateSelector.use.setUser();
  const setWallets = usePrivyStateSelector.use.setWallets();
  const setReady = usePrivyStateSelector.use.setReady();
  const setAuthenticated = usePrivyStateSelector.use.setAuthenticated();
  const setIsOpen = usePrivyStateSelector.use.setIsOpen();

  // Get actions from context (these don't change)
  const onLogin = useContextSelector(PrivyAuthContext, (auth) => auth.onLogin);
  const onLogout = useContextSelector(PrivyAuthContext, (auth) => auth.onLogout);
  const confirmLogout = useContextSelector(PrivyAuthContext, (auth) => auth.confirmLogout);
  const onCreateWallet = useContextSelector(PrivyAuthContext, (auth) => auth.onCreateWallet);

  // Sync context state with Zustand store
  const contextUser = useContextSelector(PrivyAuthContext, (auth) => auth.user);
  const contextWallets = useContextSelector(PrivyAuthContext, (auth) => auth.wallets);
  const contextReady = useContextSelector(PrivyAuthContext, (auth) => auth.ready);
  const contextAuthenticated = useContextSelector(PrivyAuthContext, (auth) => auth.authenticated);
  const contextIsOpen = useContextSelector(PrivyAuthContext, (auth) => auth.isOpen);

  const syncWithContext = () => {
    setUser(contextUser || null);
    setWallets(contextWallets || null);
    setReady(contextReady || false);
    setAuthenticated(contextAuthenticated || false);
    setIsOpen(contextIsOpen || false);
  };

  // Sync on context changes (this is the only useEffect in this hook)
  useEffect(() => {
    syncWithContext();
  }, [contextUser, contextWallets, contextReady, contextAuthenticated, contextIsOpen]);

  return {
    user,
    wallets,
    ready,
    authenticated,
    isOpen,
    onLogin,
    onLogout,
    confirmLogout,
    onCreateWallet,
    syncWithContext,
  };
};
