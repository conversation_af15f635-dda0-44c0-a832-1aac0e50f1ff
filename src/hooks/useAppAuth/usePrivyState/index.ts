import { ConnectedWallet, User } from "@privy-io/react-auth";
import { useContextSelector } from "use-context-selector";

import PrivyAuthContext from "../../../contexts/PrivyAuthContext";

export interface UsePrivyStateReturn {
  user?: User | null;
  wallets?: ConnectedWallet[] | null;
  ready?: boolean;
  authenticated?: boolean;
  isOpen: boolean;
  onLogin: () => void;
  onLogout: () => void;
  confirmLogout: (direct?: boolean) => void;
  onCreateWallet: () => Promise<void>;
}

export const usePrivyState = (): UsePrivyStateReturn => {
  const user = useContextSelector(PrivyAuthContext, (auth) => auth.user);
  const wallets = useContextSelector(PrivyAuthContext, (auth) => auth.wallets);
  const ready = useContextSelector(PrivyAuthContext, (auth) => auth.ready);
  const authenticated = useContextSelector(PrivyAuthContext, (auth) => auth.authenticated);
  const isOpen = useContextSelector(PrivyAuthContext, (auth) => auth.isOpen);

  const onLogin = useContextSelector(PrivyAuthContext, (auth) => auth.onLogin);
  const onLogout = useContextSelector(PrivyAuthContext, (auth) => auth.onLogout);
  const confirmLogout = useContextSelector(PrivyAuthContext, (auth) => auth.confirmLogout);
  const onCreateWallet = useContextSelector(PrivyAuthContext, (auth) => auth.onCreateWallet);

  return {
    user,
    wallets,
    ready,
    authenticated,
    isOpen,
    onLogin,
    onLogout,
    confirmLogout,
    onCreateWallet,
  };
};
