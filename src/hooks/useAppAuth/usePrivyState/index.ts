import { ConnectedWallet, User } from "@privy-io/react-auth";
import { usePrivy, useWallets, useLogin, useLogout } from "@privy-io/react-auth";

import { usePrivyStateSelector } from "./store";

export interface UsePrivyStateReturn {
  user: User | null;
  wallets: ConnectedWallet[] | null;
  ready: boolean;
  authenticated: boolean;
  isOpen: boolean;
  onLogin: () => void;
  onLogout: () => void;
  confirmLogout: (direct?: boolean) => void;
  onCreateWallet: () => Promise<void>;
  initializeFromPrivy: () => void;
}

export const usePrivyState = (): UsePrivyStateReturn => {
  // Get state from Zustand store
  const user = usePrivyStateSelector.use.user();
  const wallets = usePrivyStateSelector.use.wallets();
  const ready = usePrivyStateSelector.use.ready();
  const authenticated = usePrivyStateSelector.use.authenticated();
  const isOpen = usePrivyStateSelector.use.isOpen();

  // Get sync function from store
  const syncPrivyState = usePrivyStateSelector.use.syncPrivyState();

  // Get Privy hooks directly (no context)
  const { user: privyUser, ready: privyReady, authenticated: privyAuthenticated } = usePrivy();
  const { wallets: privyWallets } = useWallets();
  const { login } = useLogin();
  const { logout } = useLogout();

  // Initialize store from Privy hooks
  const initializeFromPrivy = () => {
    syncPrivyState({
      user: privyUser || null,
      wallets: privyWallets || null,
      ready: privyReady || false,
      authenticated: privyAuthenticated || false,
      isOpen: false, // This would need to be managed separately
    });
  };

  const onCreateWallet = async (): Promise<void> => {
    // This would need to be implemented based on your wallet creation logic
    // For now, just a placeholder
  };

  const confirmLogout = (direct?: boolean) => {
    if (direct) {
      logout();
    } else {
      // Could open a confirmation modal here
      logout();
    }
  };

  return {
    user,
    wallets,
    ready,
    authenticated,
    isOpen,
    onLogin: login,
    onLogout: logout,
    confirmLogout,
    onCreateWallet,
    initializeFromPrivy,
  };
};
