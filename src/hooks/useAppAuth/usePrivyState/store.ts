import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

interface PrivyStateStore {
  // Additional state that might be needed beyond the context
  lastAuthCheck: number;
  authErrors: string[];
  
  // Actions
  setLastAuthCheck: (timestamp: number) => void;
  addAuthError: (error: string) => void;
  clearAuthErrors: () => void;
}

const usePrivyStateStore = create<PrivyStateStore>((set) => ({
  lastAuthCheck: 0,
  authErrors: [],
  
  setLastAuthCheck: (timestamp) => set({ lastAuthCheck: timestamp }),
  addAuthError: (error) => set((state) => ({ 
    authErrors: [...state.authErrors, error] 
  })),
  clearAuthErrors: () => set({ authErrors: [] }),
}));

export const usePrivyStateSelector = createSelectors(usePrivyStateStore);
