import { ConnectedWallet, User } from "@privy-io/react-auth";
import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";

interface PrivyStateStore {
  // Core Privy state
  user: User | null;
  wallets: ConnectedWallet[] | null;
  ready: boolean;
  authenticated: boolean;
  isOpen: boolean;

  // Additional state
  lastAuthCheck: number;
  authErrors: string[];

  // Core actions
  setUser: (user: User | null) => void;
  setWallets: (wallets: ConnectedWallet[] | null) => void;
  setReady: (ready: boolean) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setIsOpen: (isOpen: boolean) => void;

  // Additional actions
  setLastAuthCheck: (timestamp: number) => void;
  addAuthError: (error: string) => void;
  clearAuthErrors: () => void;
  resetState: () => void;
  syncPrivyState: (privyState: {
    user: User | null;
    wallets: ConnectedWallet[] | null;
    ready: boolean;
    authenticated: boolean;
    isOpen?: boolean;
  }) => void;
}

const usePrivyStateStore = create<PrivyStateStore>((set) => ({
  // Core Privy state
  user: null,
  wallets: null,
  ready: false,
  authenticated: false,
  isOpen: false,

  // Additional state
  lastAuthCheck: 0,
  authErrors: [],

  // Core actions
  setUser: (user) => set({ user }),
  setWallets: (wallets) => set({ wallets }),
  setReady: (ready) => set({ ready }),
  setAuthenticated: (authenticated) => set({ authenticated }),
  setIsOpen: (isOpen) => set({ isOpen }),

  // Additional actions
  setLastAuthCheck: (timestamp) => set({ lastAuthCheck: timestamp }),
  addAuthError: (error) => set((state) => ({
    authErrors: [...state.authErrors, error]
  })),
  clearAuthErrors: () => set({ authErrors: [] }),
  resetState: () => set({
    user: null,
    wallets: null,
    ready: false,
    authenticated: false,
    isOpen: false,
    authErrors: [],
    lastAuthCheck: 0,
  }),

  // Sync all state at once
  syncPrivyState: (privyState: {
    user: User | null;
    wallets: ConnectedWallet[] | null;
    ready: boolean;
    authenticated: boolean;
    isOpen?: boolean;
  }) => set((state) => ({
    ...state,
    user: privyState.user,
    wallets: privyState.wallets,
    ready: privyState.ready,
    authenticated: privyState.authenticated,
    isOpen: privyState.isOpen ?? state.isOpen,
    lastAuthCheck: Date.now(),
  })),
}));

export const usePrivyStateSelector = createSelectors(usePrivyStateStore);
