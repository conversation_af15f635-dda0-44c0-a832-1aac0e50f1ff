import { useEffect } from "react";
import { usePrivy, useWallets } from "@privy-io/react-auth";

import { usePrivyStateSelector } from "./usePrivyState/store";

/**
 * Hook to sync Privy state with Zustand store
 * Should be used in a top-level component (like AppWrapper)
 * This is the ONLY place where useEffect should be used for auth sync
 */
export const usePrivySync = () => {
  const syncPrivyState = usePrivyStateSelector.use.syncPrivyState();
  
  // Get current Privy state
  const { user, ready, authenticated } = usePrivy();
  const { wallets } = useWallets();

  // Sync whenever Privy state changes
  useEffect(() => {
    syncPrivyState({
      user: user || null,
      wallets: wallets || null,
      ready: ready || false,
      authenticated: authenticated || false,
    });
  }, [user, wallets, ready, authenticated, syncPrivyState]);

  return {
    isReady: ready,
    isAuthenticated: authenticated,
    hasUser: !!user,
    hasWallets: !!(wallets && wallets.length > 0),
  };
};
