import { IValidateCodeResponse, PrivyAuthContextProps } from "@/types";
import { match } from "ts-pattern";
import { useMemo } from "react";

import { useAuthRedirect } from "./useAuthRedirect";
import { useJWTManager } from "./useJWTManager";
import { usePrivyState } from "./usePrivyState";
import { useReferralValidation } from "./useReferralValidation";

export interface UseAppAuthReturn extends PrivyAuthContextProps {
  // Referral validation state
  hasValidatedReferral: boolean;
  isValidatingReferral: boolean;
  lastValidationError: string | null;

  // Combined auth state
  canAccessApp: boolean;
  authStatus: "loading" | "unauthenticated" | "authenticated" | "validated";

  // Actions
  validateReferralCode: (code: string) => Promise<IValidateCodeResponse>;
  validateStoredReferralCode: () => Promise<void>;
  checkAuthStatus: () => { ready: boolean; authenticated: boolean; hasValidatedReferral: boolean };
  clearValidationError: () => void;
  resetValidation: () => void;

  // Redirect functions
  redirectToLanding: () => void;
  redirectToApp: () => void;
  redirectToStoredPath: () => void;
  storeCurrentPath: () => void;

  // JWT functions (enhanced)
  refreshJWT: () => Promise<string>;
  getCachedJWT: () => string | null;
  clearJWTCache: () => void;
}

export const useAppAuth = (): UseAppAuthReturn => {
  const privyState = usePrivyState();
  const referralValidation = useReferralValidation();
  const jwtManager = useJWTManager();
  const authRedirect = useAuthRedirect();

  // Ensure boolean values with defaults
  const ready = privyState.ready ?? false;
  const authenticated = privyState.authenticated ?? false;
  const hasValidatedReferral = referralValidation.hasValidatedReferral;

  const canAccessApp = authenticated && hasValidatedReferral;

  // Determine auth status using ts-pattern
  const authStatus = useMemo(() =>
    match({ ready, authenticated, hasValidatedReferral })
      .with({ ready: false }, () => "loading" as const)
      .with({ ready: true, authenticated: false }, () => "unauthenticated" as const)
      .with({ ready: true, authenticated: true, hasValidatedReferral: false }, () => "authenticated" as const)
      .with({ ready: true, authenticated: true, hasValidatedReferral: true }, () => "validated" as const)
      .exhaustive(),
    [ready, authenticated, hasValidatedReferral]
  );

  const checkAuthStatus = useMemo(() => () => ({
    ready,
    authenticated,
    hasValidatedReferral,
  }), [ready, authenticated, hasValidatedReferral]);

  const validateStoredReferralCode = useMemo(() => async (): Promise<void> => {
    await referralValidation.validateStoredCode();
  }, [referralValidation.validateStoredCode]);

  return {
    // Privy state (compatible with usePrivyAuth)
    user: privyState.user,
    wallets: privyState.wallets,
    ready,
    authenticated,
    isOpen: privyState.isOpen,
    onLogin: privyState.onLogin,
    onLogout: privyState.onLogout,
    confirmLogout: privyState.confirmLogout,
    onCreateWallet: privyState.onCreateWallet,
    getJWT: jwtManager.getJWT,

    // Referral validation state
    hasValidatedReferral,
    isValidatingReferral: referralValidation.isValidating,
    lastValidationError: referralValidation.lastValidationError,

    // Combined auth state
    canAccessApp,
    authStatus,

    // Actions
    validateReferralCode: referralValidation.validateCode,
    validateStoredReferralCode,
    checkAuthStatus,
    clearValidationError: referralValidation.clearValidationError,
    resetValidation: referralValidation.resetValidation,

    // Redirect functions
    redirectToLanding: authRedirect.redirectToLanding,
    redirectToApp: authRedirect.redirectToApp,
    redirectToStoredPath: authRedirect.redirectToStoredPath,
    storeCurrentPath: authRedirect.storeCurrentPath,

    // JWT functions (enhanced)
    refreshJWT: jwtManager.refreshJWT,
    getCachedJWT: jwtManager.getCachedJWT,
    clearJWTCache: jwtManager.clearJWTCache,
  };
};

export default useAppAuth;
