import { IValidateCodeResponse, PrivyAuthContextProps } from "@/types";

import { useAuthRedirect } from "./useAuthRedirect";
import { useJWTManager } from "./useJWTManager";
import { usePrivyState } from "./usePrivyState";
import { useReferralValidation } from "./useReferralValidation";

export interface UseAppAuthReturn extends PrivyAuthContextProps {
  // Referral validation state
  hasValidatedReferral: boolean;
  isValidatingReferral: boolean;
  lastValidationError: string | null;

  // Combined auth state
  canAccessApp: boolean;
  authStatus: "loading" | "unauthenticated" | "authenticated" | "validated";

  // Actions
  validateReferralCode: (code: string) => Promise<IValidateCodeResponse>;
  validateStoredReferralCode: () => Promise<void>;
  checkAuthStatus: () => { ready: boolean; authenticated: boolean; hasValidatedReferral: boolean };
  clearValidationError: () => void;
  resetValidation: () => void;

  // Redirect functions
  redirectToLanding: () => void;
  redirectToApp: () => void;
  redirectToStoredPath: () => void;
  storeCurrentPath: () => void;

  // JWT functions (enhanced)
  refreshJWT: () => Promise<string>;
  getCachedJWT: () => string | null;
  clearJWTCache: () => void;
}

export const useAppAuth = (): UseAppAuthReturn => {
  const privyState = usePrivyState();
  const referralValidation = useReferralValidation();
  const jwtManager = useJWTManager();
  const authRedirect = useAuthRedirect();

  // Ensure boolean values with defaults
  const ready = privyState.ready ?? false;
  const authenticated = privyState.authenticated ?? false;
  const hasValidatedReferral = referralValidation.hasValidatedReferral;

  const canAccessApp = authenticated && hasValidatedReferral;

  // Determine auth status
  const getAuthStatus = (): "loading" | "unauthenticated" | "authenticated" | "validated" => {
    if (!ready) return "loading";
    if (!authenticated) return "unauthenticated";
    if (!hasValidatedReferral) return "authenticated";
    return "validated";
  };

  const checkAuthStatus = () => ({
    ready,
    authenticated,
    hasValidatedReferral,
  });

  const validateStoredReferralCode = async (): Promise<void> => {
    await referralValidation.validateStoredCode();
  };

  return {
    // Privy state (compatible with usePrivyAuth)
    user: privyState.user,
    wallets: privyState.wallets,
    ready,
    authenticated,
    isOpen: privyState.isOpen,
    onLogin: privyState.onLogin,
    onLogout: privyState.onLogout,
    confirmLogout: privyState.confirmLogout,
    onCreateWallet: privyState.onCreateWallet,
    getJWT: jwtManager.getJWT,

    // Referral validation state
    hasValidatedReferral,
    isValidatingReferral: referralValidation.isValidating,
    lastValidationError: referralValidation.lastValidationError,

    // Combined auth state
    canAccessApp,
    authStatus: getAuthStatus(),

    // Actions
    validateReferralCode: referralValidation.validateCode,
    validateStoredReferralCode,
    checkAuthStatus,
    clearValidationError: referralValidation.clearValidationError,
    resetValidation: referralValidation.resetValidation,

    // Redirect functions
    redirectToLanding: authRedirect.redirectToLanding,
    redirectToApp: authRedirect.redirectToApp,
    redirectToStoredPath: authRedirect.redirectToStoredPath,
    storeCurrentPath: authRedirect.storeCurrentPath,

    // JWT functions (enhanced)
    refreshJWT: jwtManager.refreshJWT,
    getCachedJWT: jwtManager.getCachedJWT,
    clearJWTCache: jwtManager.clearJWTCache,
  };
};

export default useAppAuth;
