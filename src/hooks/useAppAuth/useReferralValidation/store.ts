import { createSelectors } from "@/utils/create-selectors";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface ReferralValidationStore {
  hasValidatedReferral: boolean;
  isValidating: boolean;
  lastValidationError: string | null;
  lastValidationTimestamp: number;
  validationCount: number;

  // Actions
  setHasValidatedReferral: (validated: boolean) => void;
  setIsValidating: (validating: boolean) => void;
  setLastValidationError: (error: string | null) => void;
  clearValidationError: () => void;
  resetValidation: () => void;
  incrementValidationCount: () => void;
}

const useReferralValidationStore = create<ReferralValidationStore>()(
  persist(
    (set) => ({
      hasValidatedReferral: false,
      isValidating: false,
      lastValidationError: null,
      lastValidationTimestamp: 0,
      validationCount: 0,

      setHasValidatedReferral: (validated) => set((state) => ({
        hasValidatedReferral: validated,
        lastValidationTimestamp: validated ? Date.now() : 0,
        validationCount: validated ? state.validationCount + 1 : state.validationCount,
      })),
      setIsValidating: (validating) => set({ isValidating: validating }),
      setLastValidationError: (error) => set({ lastValidationError: error }),
      clearValidationError: () => set({ lastValidationError: null }),
      incrementValidationCount: () => set((state) => ({
        validationCount: state.validationCount + 1
      })),
      resetValidation: () => set({
        hasValidatedReferral: false,
        isValidating: false,
        lastValidationError: null,
        lastValidationTimestamp: 0,
        validationCount: 0,
      }),
    }),
    {
      name: "referral-validation-storage",
      partialize: (state) => ({
        hasValidatedReferral: state.hasValidatedReferral,
        lastValidationTimestamp: state.lastValidationTimestamp,
        validationCount: state.validationCount,
      }),
    }
  )
);

export const useReferralValidationSelector = createSelectors(useReferralValidationStore);
