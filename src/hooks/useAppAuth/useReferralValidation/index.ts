import { authService } from "@/services/auth.service";
import { IValidateCodeResponse } from "@/types";
import { handleStorage } from "@/utils/storage";

import { useReferralValidationSelector } from "./store";

export interface UseReferralValidationReturn {
  hasValidatedReferral: boolean;
  isValidating: boolean;
  lastValidationError: string | null;
  lastValidationTimestamp: number;
  validateCode: (code: string) => Promise<IValidateCodeResponse>;
  validateStoredCode: () => Promise<void>;
  checkValidationStatus: () => boolean;
  clearValidationError: () => void;
  resetValidation: () => void;
  isValidationExpired: () => boolean;
}

export const useReferralValidation = (): UseReferralValidationReturn => {
  const hasValidatedReferral = useReferralValidationSelector.use.hasValidatedReferral();
  const isValidating = useReferralValidationSelector.use.isValidating();
  const lastValidationError = useReferralValidationSelector.use.lastValidationError();
  const lastValidationTimestamp = useReferralValidationSelector.use.lastValidationTimestamp();

  const setHasValidatedReferral = useReferralValidationSelector.use.setHasValidatedReferral();
  const setIsValidating = useReferralValidationSelector.use.setIsValidating();
  const setLastValidationError = useReferralValidationSelector.use.setLastValidationError();
  const clearValidationError = useReferralValidationSelector.use.clearValidationError();
  const resetValidation = useReferralValidationSelector.use.resetValidation();

  const validateCode = async (code: string): Promise<IValidateCodeResponse> => {
    try {
      setIsValidating(true);
      setLastValidationError(null);

      const response = await authService.validateCode(code);

      if (response.error) {
        const errorResponse: IValidateCodeResponse = {
          success: false,
          message: response.error,
          error: response.error,
        };
        setLastValidationError(response.error);
        return errorResponse;
      }

      setHasValidatedReferral(true);
      handleStorage("local", "inviteCode", "remove");

      return {
        success: true,
        message: response.message || "Code validated successfully",
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to validate code";
      setLastValidationError(errorMessage);

      return {
        success: false,
        message: errorMessage,
        error: errorMessage,
      };
    } finally {
      setIsValidating(false);
    }
  };

  const validateStoredCode = async (): Promise<void> => {
    const storedCode = handleStorage<string>("local", "inviteCode", "get");
    if (!storedCode) return;

    await validateCode(storedCode);
  };

  const checkValidationStatus = (): boolean => {
    return hasValidatedReferral && !isValidationExpired();
  };

  const isValidationExpired = (): boolean => {
    if (!lastValidationTimestamp) return true;

    // Consider validation expired after 24 hours
    const expiryTime = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    return Date.now() - lastValidationTimestamp > expiryTime;
  };

  return {
    hasValidatedReferral,
    isValidating,
    lastValidationError,
    lastValidationTimestamp,
    validateCode,
    validateStoredCode,
    checkValidationStatus,
    clearValidationError,
    resetValidation,
    isValidationExpired,
  };
};
