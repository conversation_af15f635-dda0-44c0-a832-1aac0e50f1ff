import { handleStorage } from "@/utils/storage";
import { useRouter } from "next/navigation";

import { redirectToLanding, redirectToApp as redirectToAppUtil, isValidRedirectPath, getDefaultAppPath } from "./utils";

export interface UseAuthRedirectReturn {
  redirectToLanding: () => void;
  redirectToApp: () => void;
  redirectToPath: (path: string) => void;
  redirectToStoredPath: () => void;
  canRedirect: () => boolean;
  getStoredPath: () => string | null;
  storeCurrentPath: () => void;
  clearStoredPath: () => void;
}

export const useAuthRedirect = (): UseAuthRedirectReturn => {
  const router = useRouter();

  const redirectToPath = (path: string) => {
    if (isValidRedirectPath(path)) {
      router.push(path);
    } else {
      router.push(getDefaultAppPath());
    }
  };

  const redirectToApp = () => {
    const storedPath = getStoredPath();
    if (storedPath && isValidRedirectPath(storedPath)) {
      router.push(storedPath);
      clearStoredPath();
    } else {
      redirectToAppUtil();
    }
  };

  const redirectToStoredPath = () => {
    const storedPath = getStoredPath();
    if (storedPath) {
      redirectToPath(storedPath);
      clearStoredPath();
    } else {
      redirectToApp();
    }
  };

  const getStoredPath = (): string | null => {
    return handleStorage<string>("session", "pathHistory", "get");
  };

  const storeCurrentPath = () => {
    if (typeof window !== "undefined") {
      const currentPath = window.location.pathname + window.location.search;
      handleStorage("session", "pathHistory", "create", currentPath);
    }
  };

  const clearStoredPath = () => {
    handleStorage("session", "pathHistory", "remove");
  };

  const canRedirect = (): boolean => {
    return typeof window !== "undefined";
  };

  return {
    redirectToLanding,
    redirectToApp,
    redirectToPath,
    redirectToStoredPath,
    canRedirect,
    getStoredPath,
    storeCurrentPath,
    clearStoredPath,
  };
};
