export const redirectToLanding = (): void => {
  if (typeof window !== "undefined") {
    window.location.href = "https://ibi.cash";
  }
};

export const redirectToApp = (): void => {
  if (typeof window !== "undefined") {
    window.location.href = "/claim";
  }
};

export const isValidRedirectPath = (path: string): boolean => {
  if (!path || typeof path !== "string") return false;

  const validPaths = [
    "/claim",
    "/profile",
    "/portfolio",
    "/rewards",
    "/landunits",
    "/manage-accounts",
    "/invite-codes",
    "/predict",
    "/bond"
  ];

  // Check exact matches
  if (validPaths.includes(path)) return true;

  // Check if path starts with valid patterns
  const validPatterns = ["/p/", "/claim/", "/profile/"];
  return validPatterns.some(pattern => path.startsWith(pattern));
};

export const getDefaultAppPath = (): string => {
  return "/claim";
};

export const sanitizePath = (path: string): string => {
  if (!path || typeof path !== "string") return getDefaultAppPath();

  // Remove any potential XSS attempts
  const cleanPath = path.replace(/[<>'"]/g, "");

  // Ensure path starts with /
  const normalizedPath = cleanPath.startsWith("/") ? cleanPath : `/${cleanPath}`;

  return isValidRedirectPath(normalizedPath) ? normalizedPath : getDefaultAppPath();
};

export const buildRedirectUrl = (basePath: string, params?: Record<string, string>): string => {
  if (!params || Object.keys(params).length === 0) {
    return basePath;
  }

  const searchParams = new URLSearchParams(params);
  return `${basePath}?${searchParams.toString()}`;
};
