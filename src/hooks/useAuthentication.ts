import { handleStorage } from "@/utils/storage";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import useCode from "./useCode";
import usePrivyAuth from "./usePrivyAuth";

/**
 * Hook to handle authentication flow and invite code validation
 * @returns Authentication state including authenticated status, ready state, and validation status
 */
const useAuthentication = () => {
  const router = useRouter();
  const { ready, authenticated } = usePrivyAuth();
  const { sendCode } = useCode();
  const [isValidating, setIsValidating] = useState(true);

  const handleValidations = async () => {
    if (!isValidating) return;

    const inviteCode = handleStorage<string>("local", "inviteCode", "get");
    if (inviteCode) {
      await sendCode();

      router.push("/claim");
      return;
    }

    router.push("/claim");
    setIsValidating(false);
  };

  useEffect(() => {
    if (authenticated) {
      handleValidations();
    }
  }, [authenticated]);

  useEffect(() => {
    if (ready && !authenticated) {
      window.location.href = "https://ibi.cash";
    }
  }, [ready, authenticated]);

  return { authenticated, ready, isValidating };
};

export default useAuthentication;
