"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { handleStorage } from "@/utils/storage";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

import EntryLoading from "../EntryLoading";
import { useAppHeaderSelector } from "../Headers/EntryHeader/header.store";
import IbiConfirmDialog from "../IbiUi/IbiConfirmDialog";

const AppWrapper = ({ children }: { children: React.ReactNode }) => {
  const { ready = false, authenticated, onLogin, confirmLogout: setConfirmLogout, isOpen } = usePrivyAuth();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const confirmLogout = useAppHeaderSelector.use.confirmLogout();
  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  useEffect(() => {
    if (ready) {
      const referralCode = searchParams?.get("referral") || searchParams?.get("rc");
      const ibicode = pathname.startsWith("/p/") ? pathname.split("/p/")[1] : null;

      if (referralCode) {
        handleStorage("local", "inviteCode", "create", referralCode);
      }
      if (ibicode) {
        handleStorage("session", "ibiCode", "create", ibicode);
      }

      const pathHistory = `${pathname}${searchParams ? `?${searchParams}` : ""}`;
      handleStorage("session", "pathHistory", "create", pathHistory);

      if (!isOpen && pathname === "/") {
        onLogin();
      }

      if (authenticated) {
        router.push("/claim");
      }
    }
  }, [ready, authenticated, isOpen]);

  return (
    <EntryLoading wrapperClass="h-full" condition={ready}>
      <>
        <div className="w-full h-full flex bg-primary-dark min-h-screen overflow-y-hidden overflow-x-hidden">
          <div className="flex-1 h-full">{children}</div>
        </div>
        <IbiConfirmDialog
          title="Confirm logout"
          description="Are you sure you want to log out?"
          open={confirmLogout}
          onOpenChange={toggleConfirmLogout}
          onCancel={toggleConfirmLogout}
          onConfirm={() => {
            setConfirmLogout(true);
            toggleConfirmLogout();
          }}
        />
      </>
    </EntryLoading>
  );
};

export default AppWrapper;
